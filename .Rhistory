box(width = 4, selectInput("Area", "State", choices = c('All', 'New South Wales', 'Victoria', 'Queensland', 'South Australia', "Tasmania", 'Western Australia', 'Northern Territory'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 4, selectInput("DistributorID", "Outlet", choices = c('None'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Sales", leafletOutput("MapSale", width = "100%", height = "600px")),
tabPanel("Frequency", leafletOutput("MapFrequency", width = "100%", height = "600px"))
)
)
)
),
# End Customer Sales tab
nav_panel("End Customer Sales",
fluidRow(
box(width = 1),
box(width = 4, selectInput("AusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 4, selectizeInput("PostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Sales", leafletOutput("CustomerMap", width = "100%", height = "600px")),
tabPanel("Frequency", leafletOutput("CustomerFrequency", width = "100%", height = "600px"))
)
)
)
),
# Lead Map tab
nav_panel("Lead Map",
fluidRow(
box(width = 1),
box(width = 3, selectInput("LeadAusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 3, selectizeInput("LeadPostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%")),
box(width = 3, selectizeInput("LeadStatus", "Status", choices = NULL, multiple = FALSE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Leads", leafletOutput("LeadMap", width = "100%", height = "600px"))
)
)
)
),
# Leads Data by Outlet tab
nav_panel("Leads Data by Outlet",
tabsetPanel(
tabPanel("Raw Data", textOutput("selectedDates"), DTOutput("rawdata")),
tabPanel("App Total", downloadButton("downloadAppData", "Download App Total Data"), DTOutput("appTotal")),
tabPanel("Web Total", downloadButton("downloadWebData", "Download Web Total Data"), DTOutput("webTotal")),
tabPanel("Bar Charts for App and Web Total",
h3("App Total Bar Chart"), plotOutput("appBarChart"),
h3("Web Total Bar Chart"), plotOutput("webBarChart")),
# New tab for monthly graph
tabPanel("Leads Distribution Over Time",
h3("Leads Distribution Over Time"),
plotOutput("leads_combined_line_chart"),
downloadButton("download_leads_data","Downloads Leads Data")
)
)
),
# Add the new FORECASTING tab
nav_panel("FORECASTING",
fluidRow(
column(6,
tags$h3("Forecasting Analysis", style = "text-align: center; color: #FF9800;"),
tags$img(src = "path/to/your/webf-file.webf",
height = "400px",
style = "display: block; margin: 0 auto;")
),
column(6,
tags$h3("Visualization GIF", style = "text-align: center; color: #FF9800;"),
tags$img(src = "path/to/your/gif-file.gif",
height = "400px",
style = "display: block; margin: 0 auto;")
)
)
),
# Data Table tab
nav_panel("Data Table",
fluidRow(
box(width = 12, DTOutput("result"))
)
)
)
)
# Define UI
ui <- page_sidebar(
title = tags$div(
"StramitDashy",
tags$img(
src = "https://www.fairdinkumbuilds.com.au/themes/fds/dist/images/standard/logo-animated.gif",
height = "60px",
width = "60px",
style = "display: inline-block; margin-left: 10px; vertical-align: middle;"
)
),
tags$head(
tags$style(HTML("
#mainTab .nav-link { font-weight: bold; color:black }
#mainTab .nav-link[data-value='Plots'] { background-color: #84A98C; }
#mainTab .nav-link[data-value='Outlet Sales'] { background-color: #84A98C; }
#mainTab .nav-link[data-value='End Customer Sales'] { background-color: #84A98C; }
#mainTab .nav-link[data-value='Lead Map'] { background-color: #84A98C; }
#mainTab .nav-link[data-value='Leads Data by Outlet'] { background-color: #84A98C; }
#mainTab .nav-link[data-value='Data Table'] { background-color: #84A98C; }
#mainTab .nav-link.active { color: white; } /* White text on active tab */
"))
),
# Custom styling for FORECASTING tab
tags$head(
tags$style(HTML("
#mainTab .nav-link[data-value='FORECASTING'] { background-color: #EBEBE4; } /* Distinct color for FORECASTING tab */
#mainTab .nav-link[data-value='FORECASTING']:hover { background-color: #FFB300; } /* Hover effect for FORECASTING tab */
#mainTab .nav-link.active[data-value='FORECASTING'] { background-color: #EBEBE4; color: white; } /* Active tab styling */
"))
),
# Sidebar panel with conditional elements based on the selected tab
sidebar = sidebar(
id = "menu1",
width = 300,
# Sidebar for all tabs except "Leads Data by Outlet"
conditionalPanel(
condition = "input.mainTab != 'Leads Data by Outlet'",
selectInput(
inputId = "Category",
label = "Choose a reporting category",
choices = c('FDB Sheds', 'SB Sheds', 'FDB Patios', 'Lifestyle', 'NZ Sheds'),
selected = c('FDB Sheds', 'SB Sheds'),
multiple = TRUE,
selectize = TRUE,
width = '100%'
),
numericInput(
inputId = "unit_price",
label = "Average Selling Price",
value = 286,
min = 200,
max = 500,
step = 1,
width = '100%'
),
dateRangeInput(
inputId = "daterange1",
label = "Date range",
start = Sys.Date() - 360,
end = Sys.Date(),
width = '100%'
),
actionButton(
inputId = "refresh",
icon = icon("refresh"),
class = "btn-sm",
label = "Refresh",
width = '100%'
),
actionButton(
inputId = "loaddata",
icon = icon("bars-progress"),
class = "btn-sm",
label = "Load Data",
width = '100%'
),
downloadButton(
outputId = "downloadData",
icon = icon("download"),
class = "btn-sm",
label = "Download",
width = '100%'
),
hr()
),
# Sidebar specifically for "Leads Data by Outlet"
conditionalPanel(
condition = "input.mainTab == 'Leads Data by Outlet'",
dateInput("startDate", "Select Start Date:", value = "2024-01-01"),
dateInput("endDate", "Select End Date:", value = Sys.Date()),
downloadButton("downloadDataFranchisee", "Download All Data")
)
),
# Main content area with tabs, ensuring the mainTab ID captures the selected tab
navset_card_underline(
id = "mainTab", # Make sure to set the id here to capture the selected tab in input.mainTab
title = "Visualisations",
useShinyjs(),
# Define the main tabs and sub-tabs
nav_panel("Plots",
fluidRow(
box(
label = "Current Stats",
width = 12,
fluidRow(
box(width = 1, textOutput('welcome', container = tags$h5)),
box(width = 2, title = h3("Today", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput("TodaySale")),
box(width = 2, title = h3("Yesterday", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "YesterdaySale")),
box(width = 2, title = h3("Week", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "ThisWeekSale")),
box(width = 2, title = h3("Last Week", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "LastWeekSale")),
box(width = 2, title = h3("Last Month", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "LastMonthSale"))
),
fluidRow(
box(width = 5, title = h3("This Month", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "ThisMonthSale")),
box(width = 2, title = h3("AU Total Commission FY2025", align = "left", style = 'font-size:18px;color:blue;'), textOutput("Commission")),
box(width = 5, title = h3("Fin Year", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "FinYearSale"))
),
fluidRow(
box(width = 5, title = h3("Number of Sheds", align = "center", style = 'font-size:18px;color:blue;'), girafeOutput(outputId = "number_sheds")),
box(width = 5, title = h3("$$$", align = "center", style = 'font-size:18px;color:blue;'), girafeOutput(outputId = "total_sales"))
)
)
)
),
# Outlet Sales tab
nav_panel("Outlet Sales",
fluidRow(
box(width = 1),
box(width = 4, selectInput("Area", "State", choices = c('All', 'New South Wales', 'Victoria', 'Queensland', 'South Australia', "Tasmania", 'Western Australia', 'Northern Territory'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 4, selectInput("DistributorID", "Outlet", choices = c('None'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Sales", leafletOutput("MapSale", width = "100%", height = "600px")),
tabPanel("Frequency", leafletOutput("MapFrequency", width = "100%", height = "600px"))
)
)
)
),
# End Customer Sales tab
nav_panel("End Customer Sales",
fluidRow(
box(width = 1),
box(width = 4, selectInput("AusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 4, selectizeInput("PostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Sales", leafletOutput("CustomerMap", width = "100%", height = "600px")),
tabPanel("Frequency", leafletOutput("CustomerFrequency", width = "100%", height = "600px"))
)
)
)
),
# Lead Map tab
nav_panel("Lead Map",
fluidRow(
box(width = 1),
box(width = 3, selectInput("LeadAusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
box(width = 3, selectizeInput("LeadPostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%")),
box(width = 3, selectizeInput("LeadStatus", "Status", choices = NULL, multiple = FALSE, width = "50%"))
),
fluidRow(
box(
width = 10, height = 16,
tabsetPanel(
tabPanel("Leads", leafletOutput("LeadMap", width = "100%", height = "600px"))
)
)
)
),
# Leads Data by Outlet tab
nav_panel("Leads Data by Outlet",
tabsetPanel(
tabPanel("Raw Data", textOutput("selectedDates"), DTOutput("rawdata")),
tabPanel("App Total", downloadButton("downloadAppData", "Download App Total Data"), DTOutput("appTotal")),
tabPanel("Web Total", downloadButton("downloadWebData", "Download Web Total Data"), DTOutput("webTotal")),
tabPanel("Bar Charts for App and Web Total",
h3("App Total Bar Chart"), plotOutput("appBarChart"),
h3("Web Total Bar Chart"), plotOutput("webBarChart")),
# New tab for monthly graph
tabPanel("Leads Distribution Over Time",
h3("Leads Distribution Over Time"),
plotOutput("leads_combined_line_chart"),
downloadButton("download_leads_data","Downloads Leads Data")
)
)
),
# Add the new FORECASTING tab
nav_panel("FORECASTING",
fluidRow(
column(6,
tags$h3("Forecasting Analysis", style = "text-align: center; color: #FF9800;"),
tags$img(src = "path/to/your/webf-file.webf",
height = "400px",
style = "display: block; margin: 0 auto;")
),
column(6,
tags$h3("Visualization GIF", style = "text-align: center; color: #FF9800;"),
tags$img(src = "path/to/your/gif-file.gif",
height = "400px",
style = "display: block; margin: 0 auto;")
)
)
),
# Data Table tab
nav_panel("Data Table",
fluidRow(
box(width = 12, DTOutput("result"))
)
)
)
)
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
shiny::runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
runApp()
shiny::runApp()
shiny::runApp()
runApp()
runApp()
runApp()
shiny::runApp()
runApp()
shiny::runApp()
runApp('C:/Users/<USER>/Desktop/All in ONE DESKTOP/TASKS/DASHBOARD R SHINY/integration of new tools into dashboard/3_colored_and logo UP3_monthlyGraph_UP4_DONE')
runApp('C:/Users/<USER>/Desktop/All in ONE DESKTOP/TASKS/DASHBOARD R SHINY/DataReporting-main (3)/DataReporting-main')
runApp()
shiny::runApp()
shiny::runApp()
runApp()
shiny::runApp()
```r
# Load libraries AND DATA
library(tidyverse)
library(lubridate)
library(keras)
library(tensorflow)
library(reticulate)
library(openxlsx)
library(readxl)
library(forecast)
rm(list=ls())
setwd("C:/Users/<USER>/Desktop/All in ONE DESKTOP/TASKS/LSTM Tony/Gokhan/Latest versions/January 2025 Data/RUN_SEP (1)/RUN_SEP/")
data <- read_excel(paste(getwd(), "/Sep_Data.xlsx", sep = ""), sheet = "Sheet1", skip = 0, col_names = TRUE)
# Normalize data
normalize <- function(x) {
return ((x - min(x)) / (max(x) - min(x)))
}
# Convert predictions back to original scale
denormalize <- function(norm_value, original_data) {
return ((norm_value * (max(original_data) - min(original_data))) + min(original_data))
}
# Create lagged variables for orders
data <- data %>%
mutate(
Order_Lag_5 = lag(Order, 5),
Order_Lag_6 = lag(Order, 6)
) %>%
na.omit()
# Parameters for model
Trainsize <- 0.85  # split ratio
FutureIR <- 3.6  # 6 months later, dropping 0.75%  CURRENT=435
predictionduration <- 12
# Setup result matrix
result <- data.frame(matrix(nrow = nrow(data) + predictionduration, ncol = 10))
colnames(result) <- c("Date", "Actual", "Lag5_100", "Lag5_110", "Lag5_120", "Lag5_130", "Lag6_100", "Lag6_110", "Lag6_120", "Lag6_130")
lagindex <- c(1, 2)
leadindex <- c(1, 2, 3, 4)
data$Date <- as.POSIXct(data$Date, format = "%d/%m/%Y")
for (z in 1:length(lagindex)) {
ordercolindex <- lagindex[z] + 3
# Normalize the data
data_norm <- data %>%
mutate(
Sales = normalize(data[[ordercolindex]]),
Leads = normalize(Leads_Database)
) %>%
na.omit()
# leads routine
leadpattern <- tail(data_norm$Leads, 12) * (1 - (leadindex[i] - 1) * 10 / 100)
# Replace the last 12 records of Leads data with lead pattern
data_norm$Leads[(nrow(data_norm) - 11):nrow(data_norm)] <- leadpattern
# Convert data to matrices
x_data <- as.matrix(data_norm %>% select(Leads))
y_data <- as.matrix(data_norm$Sales)
# Train-test split
train_size <- floor(Trainsize * nrow(x_data))
x_train <- x_data[1:train_size, , drop = FALSE]
y_train <- y_data[1:train_size, , drop = FALSE]
x_test <- x_data[(train_size + 1):nrow(x_data), , drop = FALSE]
y_test <- y_data[(train_size + 1):nrow(x_data), , drop = FALSE]
# Define the LSTM model
model <- keras_model_sequential() %>%
layer_lstm(units = 50, return_sequences = TRUE, input_shape = c(ncol(x_train), 1)) %>%
layer_lstm(units = 50) %>%
layer_dense(units = 1)
# Compile the model
model %>% compile(
loss = 'mean_squared_error',
optimizer = 'adam'
)
# Reshape data for LSTM
x_train <- array_reshape(x_train, c(nrow(x_train), ncol(x_train), 1))
x_test <- array_reshape(x_test, c(nrow(x_test), ncol(x_test), 1))
# Train the model
history <- model %>% fit(
x_train, y_train,
epochs = 100,
batch_size = 32,
validation_split = 0.2
)
# Make predictions
predictions <- model %>% predict(x_test)
predictions <- denormalize(predictions, data[[ordercolindex]])
y_test <- denormalize(y_test, data[[ordercolindex]])
y_train <- denormalize(y_train, data[[ordercolindex]])
# Prepare data for plotting original model predictions
train_dates <- data$Date[1:train_size]
test_dates <- data$Date[(train_size + 1):nrow(data)]
plot_data_original <- data.frame(
Date = c(train_dates, test_dates),
Actual = c(y_train, y_test),
Predicted = c(rep(NA, length(y_train)), predictions)
)
# Determine the maximum value for y-axis limit
max_y_value_original <- max(plot_data_original$Actual, plot_data_original$Predicted, na.rm = TRUE)
originalplottitle <- paste("Lag_", lagindex[z] + 4, "_", (1 - (leadindex[i] - 1) * 10 / 100) * 100, "Percent Original Leads")
# Plot the results
ggplot(plot_data_original, aes(x = Date)) +
geom_line(aes(y = Actual, color = "Actual Sales")) +
geom_line(aes(y = Predicted, color = "Predicted Sales")) +
labs(title = originalplottitle, x = "Date", y = "Sales") +
scale_color_manual(values = c("Actual Sales" = "black", "Predicted Sales" = "red")) +
scale_y_continuous(limits = c(0, max_y_value_original * 1.1)) +  # Adjust y-axis limit based on the maximum value
theme_minimal()
plotoriginalfilename <- paste(originalplottitle, ".png", sep = "")
ggsave(plotoriginalfilename)
# Prepare data for forecasting
full_x_data <- rbind(x_train, x_test)
full_y_data <- c(y_train, y_test)
full_y_data <- normalize(full_y_data)
# Train the model on full data
full_model <- keras_model_sequential() %>%
layer_lstm(units = 50, return_sequences = TRUE, input_shape = c(ncol(full_x_data), 1)) %>%
layer_lstm(units = 50) %>%
layer_dense(units = 1)
full_model %>% compile(
loss = 'mean_squared_error',
optimizer = 'adam'
)
full_x_data <- array_reshape(full_x_data, c(nrow(full_x_data), ncol(full_x_data), 1))
full_model %>% fit(
full_x_data, full_y_data,
epochs = 100,
batch_size = 32,
validation_split = 0.2
)
# Combine the future data into a new data frame
last_12_months_leads <- tail(data_norm$Leads, 12)
future_leads <- matrix(last_12_months_leads, nrow = 12, ncol = 1)
future_data <- array_reshape(future_leads, c(nrow(future_leads), ncol(future_leads), 1))
# Forecast future values
future_predictions <- full_model %>% predict(future_data)
future_predictions <- denormalize(future_predictions, data[[ordercolindex]])
data$Date <- as.Date(data$Date, format = "%Y-%m-%d")
# Combine historical and forecasted data
forecast_dates <- seq(max(data$Date, na.rm = TRUE) %m+% months(1), by = "month", length.out = 12)
full_dates <- c(data$Date, forecast_dates)
full_actual <- c(full_y_data, rep(NA, 12))
full_predicted <- c(rep(NA, length(full_y_data)), future_predictions)
# Calculate standard error of predictions on the test set
prediction_error <- y_test - predictions
se <- sd(prediction_error)
# Calculate confidence intervals
upper_bound <- future_predictions + 1.96 * se
lower_bound <- future_predictions - 1.96 * se
# Prepare data for plotting
plot_data <- data.frame(
Date = full_dates,
Actual = denormalize(full_actual, data[[ordercolindex]]),
Predicted = full_predicted,
Upper = c(rep(NA, length(full_y_data)), upper_bound),
Lower = c(rep(NA, length(full_y_data)), lower_bound)
)
result[1:nrow(plot_data), (z - 1) * 4 + 2 + i] <- plot_data$Predicted
# Determine the maximum value for y-axis limit
max_y_value <- max(plot_data$Actual, plot_data$Upper, na.rm = TRUE)
forecasttitle <- paste("Forecast_", originalplottitle)
# Plot the results
geom_line(aes(y = Actual, color = "Actual Sales")) +
geom_ribbon(aes(ymin = Lower, ymax
