
# Load libraries AND DATA
#####
library(tidyverse)
library(lubridate)
require(RSNNS)
library(reticulate)
require(openxlsx)
require(readxl)
require(forecast)

rm(list=ls())

setwd("D:/PANT/Business outlook/RUN_SEP/")
data <-read_excel(paste(getwd(),
                        "/Sep_Data.xlsx",sep = ""),
                  sheet = "Sheet1",
                  skip = 0,col_names = TRUE)

# Normalize data
normalize <- function(x) {
  return ((x - min(x)) / (max(x) - min(x)))
}
# Convert predictions back to original scale
denormalize <- function(norm_value, original_data) {
  return ((norm_value * (max(original_data) - min(original_data))) + min(original_data))
}
# Function to perform cross-validation and select the best model
train_best_model <- function(x_train, y_train, n_folds = 5, size = c(50), learnFuncParams = c(0.1), maxit = 500, linOut = TRUE) {
  set.seed(123)  # Set seed for reproducibility
  n <- nrow(x_train)
  folds <- sample(rep(1:n_folds, length.out = n))
  best_model <- NULL
  best_mse <- Inf

  for (fold in 1:n_folds) {
    train_indices <- which(folds != fold)
    test_indices <- which(folds == fold)

    x_train_fold <- x_train[train_indices, , drop=FALSE]
    y_train_fold <- y_train[train_indices, , drop=FALSE]
    x_test_fold <- x_train[test_indices, , drop=FALSE]
    y_test_fold <- y_train[test_indices, , drop=FALSE]

    model <- elman(x_train_fold, y_train_fold, size = size, learnFuncParams = learnFuncParams, maxit = maxit, linOut = linOut)
    predictions <- predict(model, x_test_fold)
    mse <- mean((y_test_fold - predictions)^2)

    if (mse < best_mse) {
      best_mse <- mse
      best_model <- model
    }
  }

  return(best_model)
}
# Create lagged variables for orders


data <- data %>%
  mutate(
    Order_Lag_5 = lag(Order, 5),
    Order_Lag_6 = lag(Order, 6)
  ) %>%
  na.omit()

#####
# Parameters for model
Trainsize<-0.85  #split ratio
FutureIR<-3.6 #6 months later, dropping 0.75%  CURRENT=435
predictionduration<- 12

# Setup result matrix
result<- data.frame(matrix(nrow=nrow(data)+predictionduration,
                           ncol=10))
colnames(result)<-c("Date","Actual","Lag5_100","Lag5_110","Lag5_120","Lag5_130",
                    "Lag6_100","Lag6_110","Lag6_120","Lag6_130")
lagindex<-c(1,2)
leadindex<-c(1,2,3,4)


data$Date <- as.POSIXct(data$Date,
                                    format = "%d/%m/%Y")
for (z in 1:length(lagindex)) {
  for (i in 1:length(leadindex)) {
    ordercolindex<- lagindex[z]+3
    # Normalize the data
    data_norm <- data %>%
      mutate(
        Sales = normalize(data[[ordercolindex]]),
        Leads = normalize(Leads_Database)
      ) %>%
      na.omit()
    # leads routine

    leadpattern<- tail(data_norm$Leads, 12)*(1- (leadindex[i]-1)*10/100)

    # Replace the last 12 records of Leads data with lead pattern
    data_norm$Leads[(nrow(data_norm) - 11):nrow(data_norm)] <- leadpattern

    # Convert data to matrices
    x_data <- as.matrix(data_norm %>% select(Leads))
    y_data <- as.matrix(data_norm$Sales)

    # Train-test split
    train_size <- floor(Trainsize * nrow(x_data))
    x_train <- x_data[1:train_size, , drop=FALSE]
    y_train <- y_data[1:train_size, , drop=FALSE]
    x_test <- x_data[(train_size + 1):nrow(x_data), , drop=FALSE]
    y_test <- y_data[(train_size + 1):nrow(x_data), , drop=FALSE]

    # Train the best model using cross-validation
    best_model <- train_best_model(x_train,
                                   y_train,
                                   n_folds = 10, size = c(50),
                                   learnFuncParams = c(0.1),
                                   maxit = 500, linOut = TRUE)

    # Make predictions using the best model
    predictions <- predict(best_model, x_test)


    predictions <- denormalize(predictions, data[[ordercolindex]])

    y_test <- denormalize(y_test, data[[ordercolindex]])
    y_train <- denormalize(y_train, data[[ordercolindex]])

    # Prepare data for plotting original model predictions
    train_dates <- data$Date[1:train_size]
    test_dates <- data$Date[(train_size + 1):nrow(data)]

    plot_data_original <- data.frame(
      Date = c(train_dates, test_dates),
      Actual = c(y_train, y_test),
      Predicted = c(rep(NA, length(y_train)), predictions)
    )
    # Determine the maximum value for y-axis limit
    max_y_value_original <- max(plot_data_original$Actual,
                                plot_data_original$Predicted,
                                na.rm = TRUE)

    originalplottitle<- paste("Lag_",lagindex[z]+4,"_",

                              (1- (leadindex[i]-1)*10/100)*100,"Percent Original Leads")
    # last_point <- plot_data_original %>%
    #   filter(Date == max(Date))

    # Plot the results
    ggplot(plot_data_original, aes(x = Date)) +
      geom_line(aes(y = Actual, color = "Actual Sales")) +
      geom_line(aes(y = Predicted, color = "Predicted Sales")) +
      labs(title = originalplottitle, x = "Date", y = "Sales") +
      scale_color_manual(values = c("Actual Sales" = "black", "Predicted Sales" = "red")) +
      scale_y_continuous(limits = c(0, max_y_value_original * 1.1)) +  # Adjust y-axis limit based on the maximum value
      theme_minimal()
    # geom_text(data = last_point, aes(y = Actual, label = round(Actual, 2)), vjust = -1, color = "black") +
    # geom_text(data = last_point, aes(y = Predicted, label = round(Predicted, 2)), vjust = 1, color = "red")
    #
    plotoriginalfilename<- paste(originalplottitle,".png",sep = "")
    ggsave(plotoriginalfilename)


    # Prepare data for forecasting
    full_x_data <- rbind(x_train, x_test)
    full_y_data <- c(y_train, y_test)

    full_y_data<-normalize(full_y_data)

    # Train the model on full data
    full_model <- elman(full_x_data, full_y_data,
                        size = c(50), learnFuncParams = c(0.1),
                        maxit = 500, linOut = TRUE)


    # Combine the future data into a new data frame
    last_12_months_leads <- tail(data_norm$Leads, 12)


    future_leads <- matrix(last_12_months_leads, nrow = 12, ncol = 1)
    future_data <- data.frame(Leads = future_leads)



    # Forecast future values
    future_predictions <- predict(best_model, future_data)
    future_predictions <- denormalize(future_predictions, data[[ordercolindex]])
    data$Date<-as.Date(data$Date)

    data$Date <- as.Date(data$Date, format = "%Y-%m-%d")
    # Combine historical and forecasted data
    forecast_dates <- seq(max(data$Date, na.rm = TRUE) %m+% months(1),
                          by = "month",
                          length.out = 12)
    full_dates <- c(data$Date, forecast_dates)
    full_actual <- c(full_y_data, rep(NA, 12))
    full_predicted <- c(rep(NA, length(full_y_data)), future_predictions)

    # Calculate standard error of predictions on the test set
    prediction_error <- y_test - predictions
    se <- sd(prediction_error)

    # Calculate confidence intervals
    upper_bound <- future_predictions + 1.96 * se
    lower_bound <- future_predictions - 1.96 * se
    # Prepare data for plotting
    plot_data <- data.frame(
      Date = full_dates,
      Actual = denormalize(full_actual,data[[ordercolindex]]),
      Predicted = full_predicted,
      Upper = c(rep(NA, length(full_y_data)), upper_bound),
      Lower = c(rep(NA, length(full_y_data)), lower_bound)

    )
    result[1: nrow(plot_data),(z-1)*4+2+i]<-plot_data$Predicted

    # Determine the maximum value for y-axis limit
    max_y_value <- max(plot_data$Actual, plot_data$Upper, na.rm = TRUE)

    # Prepare data for annotation
    annotation_data <- plot_data %>%
      filter(!is.na(Predicted)) %>%
      mutate(label = round(Predicted, 2))

    forecasttitle<- paste("Forecast_",originalplottitle)

    # Plot the results
    ggplot(plot_data, aes(x = Date)) +
      geom_line(aes(y = Actual, color = "Actual Sales")) +
      geom_line(aes(y = Predicted, color = "Predicted Sales")) +
      geom_ribbon(aes(ymin = Lower, ymax = Upper), fill = "blue", alpha = 0.2) +
      labs(title = forecasttitle, x = "Date", y = "Sales") +
      scale_color_manual(values = c("Actual Sales" = "black", "Predicted Sales" = "red", "Leads Marketing" = "green")) +
      scale_y_continuous(limits = c(0, max_y_value * 1.1)) +  # Adjust y-axis limit based on the maximum value
      theme_minimal()


    plotoriginalfilename1<- paste(forecasttitle,".png",sep = "")
    ggsave(plotoriginalfilename1)










  }
}

result[1: nrow(plot_data),1]<-plot_data$Date
result[1: nrow(plot_data),2]<-plot_data$Actual


write.csv(result,"Sep_forecastresult.csv")





